---
home: true
layout: Blog
icon: house
title: 博客主页
# heroImage: /logo.webp
# heroImageStyle: "border-radius: 50%"
bgImage: https://website-1300358855.cos.ap-guangzhou.myqcloud.com/bgImage.webp
heroText: <PERSON>'s blog
heroFullScreen: true
tagline: 记录 ｜ 学习 ｜ 分享
projects:
  - icon: folder-open
    name: 项目名称
    desc: 项目详细描述
    link: https://你的项目链接

  - icon: link
    name: 链接名称
    desc: 链接详细描述
    link: https://链接地址

  - icon: book
    name: 书籍名称
    desc: 书籍详细描述
    link: https://你的书籍链接

  - icon: newspaper
    name: 文章名称
    desc: 文章详细描述
    link: https://你的文章链接

  - icon: user-group
    name: 伙伴名称
    desc: 伙伴详细介绍
    link: https://你的伙伴链接

  - icon: https://theme-hope-assets.vuejs.press/logo.svg
    name: 自定义项目
    desc: 自定义详细介绍
    link: https://你的自定义链接

footer: 自定义你的页脚文字
---

这是一个博客主页的案例。

要使用此布局，你应该在页面前端设置 `layout: Blog` 和 `home: true`。

相关配置文档请见 [博客主页](https://theme-hope.vuejs.press/zh/guide/blog/home.html)。
